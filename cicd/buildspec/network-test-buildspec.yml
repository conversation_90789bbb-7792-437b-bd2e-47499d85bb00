version: 0.2

env:
  variables:
    ENVIRONMENT: "prd"

phases:
  install:
    runtime-versions:
      python: 3.9
    commands:
      - echo "=== Network Connectivity Test ==="
      - echo "Testing external connectivity from CodeBuild environment"
      
  pre_build:
    commands:
      - echo "=== Testing DNS Resolution ==="
      - nslookup archive.ubuntu.com || echo "DNS resolution failed for archive.ubuntu.com"
      - nslookup security.ubuntu.com || echo "DNS resolution failed for security.ubuntu.com"
      - nslookup pypi.org || echo "DNS resolution failed for pypi.org"
      - nslookup files.pythonhosted.org || echo "DNS resolution failed for files.pythonhosted.org"
      - nslookup cli.github.com || echo "DNS resolution failed for cli.github.com"
      - nslookup api.github.com || echo "DNS resolution failed for api.github.com"
      
  build:
    commands:
      - echo "=== Testing HTTPS Connectivity ==="
      - echo "Testing archive.ubuntu.com..."
      - curl -I --connect-timeout 10 --max-time 30 https://archive.ubuntu.com/ || echo "FAILED: archive.ubuntu.com"
      
      - echo "Testing security.ubuntu.com..."
      - curl -I --connect-timeout 10 --max-time 30 https://security.ubuntu.com/ || echo "FAILED: security.ubuntu.com"
      
      - echo "Testing pypi.org..."
      - curl -I --connect-timeout 10 --max-time 30 https://pypi.org/ || echo "FAILED: pypi.org"
      
      - echo "Testing files.pythonhosted.org..."
      - curl -I --connect-timeout 10 --max-time 30 https://files.pythonhosted.org/ || echo "FAILED: files.pythonhosted.org"
      
      - echo "Testing cli.github.com..."
      - curl -I --connect-timeout 10 --max-time 30 https://cli.github.com/ || echo "FAILED: cli.github.com"
      
      - echo "Testing api.github.com..."
      - curl -I --connect-timeout 10 --max-time 30 https://api.github.com/ || echo "FAILED: api.github.com"
      
  post_build:
    commands:
      - echo "=== Network Test Completed ==="
      - echo "Check the logs above for connectivity results"
