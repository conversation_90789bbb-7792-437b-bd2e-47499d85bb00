# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

TIS DLPF Application is a multi-component enterprise system consisting of:
- **Java Spring Boot Web Application** (`webapp/`): RESTful API service with Spring Boot 3.2.1, MyBatis, PostgreSQL/H2
- **AWS Glue Jobs** (`job/`): Python 3.9 data processing pipeline with ETL operations
- **AWS Infrastructure** (`cloudformation/`): CloudFormation templates for AWS resource deployment
- **CI/CD Pipeline** (`cicd/`): Automated deployment scripts and build configurations

## Development Commands

### Java Web Application (webapp/)
```bash
# Build and run Spring Boot application
cd webapp
./mvnw spring-boot:run

# Run tests with coverage (JaCoCo)
./mvnw test

# Build application
./mvnw clean package

# Test coverage check (50% minimum required)
./mvnw jacoco:check
```

### Python Glue Jobs (job/)
```bash
# Setup Python 3.9 environment
cd job
uv venv .venv
source .venv/bin/activate

# Install dependencies (including dev tools: black, pylint, pytest)
uv sync --all-groups --reinstall

# Run unit tests with coverage
pytest --cov --cov-config=.coveragerc

# Format code with black
black source/ test/

# Run linting with pylint
pylint source/

# Build and deploy AWS Glue packages
./build_and_deploy_dev.sh  # For development environment
./build_and_deploy_stg.sh  # For staging environment
```

### Infrastructure Deployment
```bash
# Deploy CloudFormation templates
./cloudformation/scripts/cfn_deploy.sh -e dev -n -y <resource_type> <resource_name>

# Deploy to staging
./cloudformation/scripts/cfn_deploy.sh -e stg -n -y <resource_type> <resource_name>
```

## Architecture

### Java Application (webapp/)
- **Spring Boot 3.2.1** with Java 17
- **MyBatis** for database ORM with XML mappers
- **PostgreSQL** (production) / **H2** (development) databases
- **Spring Security** for authentication
- **Log4j2** for logging
- **Controllers**: REST endpoints for customers, orders, subscriptions, health checks
- **Services**: Business logic layer
- **Mappers**: MyBatis database access layer
- **DTOs**: Data transfer objects for API requests/responses

### Python ETL Jobs (job/)
- **AWS Glue Jobs**: Data transformation and ETL processing
- **Data Converters**: Format conversion (CSV/TSV, XML, fixed-width, database)
- **Database Connectors**: PostgreSQL integration with SQLAlchemy
- **File Processors**: S3 file operations, SFTP transfers, compression
- **Configuration-driven**: YAML-based ETL configurations in `source/config/converter/`

### AWS Infrastructure (cloudformation/)
- **Step Functions**: Orchestrate data processing workflows
- **Glue Jobs**: ETL job definitions
- **Lambda Functions**: Utility functions for file checks and array operations
- **Secrets Manager**: Secure credential storage
- **Event Bridge**: Event-driven architecture
- **Security Groups**: Network access control

## Key Configuration Files

- `webapp/pom.xml`: Maven dependencies and build configuration
- `job/pyproject.toml`: Python dependencies and project metadata
- `job/uv.lock`: Locked dependency versions
- `cloudformation/environments/`: Environment-specific parameters (dev/stg/prd)

## Testing Guidelines

### Java Tests
- Unit tests in `webapp/src/test/java/`
- Integration tests for controllers and services
- Minimum 50% code coverage enforced by JaCoCo

### Python Tests
- Unit tests in `job/test/`
- Comprehensive test coverage for ETL components
- Mock AWS services using moto library

## Branch Strategy

- **develop**: Development environment deployments
- **release**: Staging environment deployments
- **main**: Production deployments

## Environment-Specific Notes

- **Development**: Uses account `************`
- **Staging**: Uses account `************`
- **Production**: Uses account `************`
- Always verify branch alignment with target environment before deployment
